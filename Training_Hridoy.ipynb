{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9a2d5fde", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/sb3_env/lib/python3.9/site-packages/gymnasium/spaces/box.py:235: UserWarning: \u001b[33mWARN: Box low's precision lowered by casting to float32, current low.dtype=float64\u001b[0m\n", "  gym.logger.warn(\n", "/Users/<USER>/miniconda3/envs/sb3_env/lib/python3.9/site-packages/gymnasium/spaces/box.py:305: UserWarning: \u001b[33mWARN: Box high's precision lowered by casting to float32, current high.dtype=float64\u001b[0m\n", "  gym.logger.warn(\n"]}, {"ename": "NameError", "evalue": "name 'policy_kwargs' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 191\u001b[0m\n\u001b[1;32m    185\u001b[0m \u001b[38;5;66;03m# Create environment\u001b[39;00m\n\u001b[1;32m    186\u001b[0m env \u001b[38;5;241m=\u001b[39m Monitor(UAVEnvironment())\n\u001b[1;32m    188\u001b[0m model \u001b[38;5;241m=\u001b[39m PPO(\n\u001b[1;32m    189\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMlpPolicy\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    190\u001b[0m     env,\n\u001b[0;32m--> 191\u001b[0m     policy_kwargs\u001b[38;5;241m=\u001b[39m\u001b[43mpolicy_kwargs\u001b[49m,\n\u001b[1;32m    192\u001b[0m     learning_rate\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.0001\u001b[39m,\n\u001b[1;32m    193\u001b[0m     gamma\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.99\u001b[39m,\n\u001b[1;32m    194\u001b[0m     gae_lambda\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.98\u001b[39m,\n\u001b[1;32m    195\u001b[0m     n_steps\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m10000\u001b[39m,\n\u001b[1;32m    196\u001b[0m     batch_size\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1000\u001b[39m,\n\u001b[1;32m    197\u001b[0m     clip_range\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.2\u001b[39m,\n\u001b[1;32m    198\u001b[0m     ent_coef\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.01\u001b[39m,  \u001b[38;5;66;03m# Entropy coefficient for exploration\u001b[39;00m\n\u001b[1;32m    199\u001b[0m     verbose\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m,\n\u001b[1;32m    200\u001b[0m     tensorboard_log\u001b[38;5;241m=\u001b[39m\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m./ppo_uav_tensorboard/PPO_\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mint\u001b[39m(time\u001b[38;5;241m.\u001b[39mtime())\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m  \u001b[38;5;66;03m# Unique log for each run\u001b[39;00m\n\u001b[1;32m    201\u001b[0m )\n\u001b[1;32m    202\u001b[0m \u001b[38;5;66;03m# Train for 10,000,000 timesteps\u001b[39;00m\n\u001b[1;32m    203\u001b[0m \u001b[38;5;66;03m# Train for 10,000,000 timesteps\u001b[39;00m\n\u001b[1;32m    204\u001b[0m total_timesteps \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1000000\u001b[39m\n", "\u001b[0;31mNameError\u001b[0m: name 'policy_kwargs' is not defined"]}], "source": ["import gymnasium as gym\n", "from gymnasium import Env\n", "from gymnasium.spaces import Box\n", "import numpy as np\n", "import torch \n", "import torch.nn as nn\n", "from stable_baselines3 import PPO\n", "from stable_baselines3.common.monitor import Monitor\n", "import matplotlib.pyplot as plt\n", "import time\n", "\n", "\n", "class UAVEnvironment(gym.Env):\n", "    \n", "    def __init__(self):\n", "        super(UAVEnvironment, self).__init__()\n", "        self.num_users = 100  \n", "        self.num_subchannels = 5  \n", "        self.W_s = 100  \n", "        self.N_0 = (10 ** (-80 / 10)) / 1000  \n", "        self.max_power = 1.0  \n", "        self.R_min = 3.0  \n", "        self.time_granularity = 0.1  \n", "        self.max_acceleration = 10  # INCREASED for more UAV movement  \n", "        self.z_movement_factor = 2  # New: Ensures more vertical movement\n", "\n", "        self.uav_position = np.array([500, 500, 100])  # Start at a higher altitude  \n", "        self.prev_velocity = np.array([0.0, 0.0, 0.0])  \n", "        \n", "        self.action_space = Box(\n", "            low=np.zeros(6 + 2 * self.num_users * self.num_subchannels),  \n", "            high=np.ones(6 + 2 * self.num_users * self.num_subchannels),  \n", "            dtype=np.float32\n", "        )\n", "\n", "        obs_size = 3 + 3 + self.num_users  \n", "        self.observation_space = Box(low=0, high=1, shape=(obs_size,), dtype=np.float32)\n", "\n", "        self.position_violations = 0\n", "        self.acceleration_violations = 0\n", "        self.users_served_per_timestep = []\n", "        self.throughput_tracker = np.zeros((self.num_users,))\n", "\n", "        self.reset(seed=42)\n", "\n", "    def reset(self, seed=None, options=None):\n", "        self.uav_position = np.array([500.0, 500.0, 100.0])  \n", "        self.prev_velocity = np.array([0.0, 0.0, 0.0])\n", "        \n", "        self.users_positions = np.random.uniform(0, 1000, size=(self.num_users, 2))\n", "        self.users_positions = np.column_stack((self.users_positions, np.zeros(self.num_users)))  \n", "        \n", "        self._update_channel_gains()\n", "        \n", "        self.step_count = 0  \n", "        self.cumulative_reward = 0  \n", "        return self._get_state(), {}\n", "\n", "    def _update_channel_gains(self):\n", "        a, b = 0.28, 9.6  \n", "        H = self.uav_position[2]\n", "        r_nj = np.linalg.norm(self.users_positions[:, :2] - self.uav_position[:2], axis=1)\n", "\n", "        theta = np.arctan(H / (r_nj + 1e-8)) * 180 / np.pi\n", "        P_LoS = 1 / (1 + a * np.exp(-b * (theta - a)))\n", "        P_NLoS = 1 - P_LoS\n", "\n", "        fc = 2e9  \n", "        c = 3e8   \n", "        d_nj = np.sqrt(H**2 + r_nj**2)\n", "\n", "        PL_LoS = 20 * np.log10(4 * np.pi * fc * d_nj / c) + 1\n", "        PL_NLoS = 20 * np.log10(4 * np.pi * fc * d_nj / c) + 20\n", "\n", "        PL = P_LoS * PL_LoS + P_NLoS * PL_NLoS\n", "        self.channel_gains = 10 ** (-PL / 10)\n", "\n", "    def step(self, action):\n", "        # Extract acceleration parameters\n", "        a_mean, a_var = action[0], action[1]\n", "        theta_mean, theta_var = action[2], action[3]\n", "        phi_mean, phi_var = action[4], action[5]\n", "\n", "        # Sample and scale acceleration\n", "        a_sample = np.random.beta(a_mean + 1, a_var + 1)\n", "        theta_sample = np.random.beta(theta_mean + 1, theta_var + 1)\n", "        phi_sample = np.random.beta(phi_mean + 1, phi_var + 1)\n", "\n", "        a = np.log(1 + np.exp(a_sample)) * self.max_acceleration  \n", "        theta = (np.log(1 + np.exp(theta_sample)) * 2 * np.pi) - np.pi  \n", "        phi = (np.log(1 + np.exp(phi_sample)) * 2 * np.pi) - np.pi  \n", "\n", "        ax = a * np.sin(theta) * np.cos(phi)\n", "        ay = a * np.sin(theta) * np.sin(phi)\n", "        az = a * np.cos(theta) * self.z_movement_factor  # Z-axis scaling\n", "\n", "        acceleration = np.array([ax, ay, az])\n", "\n", "        # Update velocity and position\n", "        self.prev_velocity += self.time_granularity * acceleration\n", "        self.uav_position += self.prev_velocity * self.time_granularity\n", "\n", "        # Clip position\n", "        self.uav_position[2] = max(self.uav_position[2], 10)  # Ensure Z > 10\n", "        # Process power allocation (fixed slicing)\n", "        power_means = action[6: 6 + self.num_users * self.num_subchannels]\n", "        power_vars = action[6 + self.num_users * self.num_subchannels: ]  # Extract remaining values\n", "\n", "        power_means = power_means.reshape(self.num_users, self.num_subchannels)\n", "        power_vars = power_vars.reshape(self.num_users, self.num_subchannels)\n", "\n", "        self.power_allocations = self._apply_softmax_power_allocation(np.stack([power_means, power_vars], axis=-1))\n", "\n", "        # Compute throughput and reward\n", "        throughputs = self._calculate_throughputs()\n", "        users_served = np.sum((throughputs >= self.R_min).astype(int))\n", "        reward = users_served\n", "\n", "        self.cumulative_reward += reward\n", "        self.step_count += 1\n", "        terminated = self.step_count >= 1000\n", "        truncated = False\n", "\n", "        return self._get_state(), reward, terminated, truncated, {}\n", "\n", "    def _calculate_throughputs(self):\n", "        throughputs = np.zeros(self.num_users)\n", "        for user in range(self.num_users):\n", "            snr = self.channel_gains[user] / self.N_0\n", "            throughputs[user] = self.W_s * np.log2(1 + snr)\n", "        return throughputs\n", "    \n", "    def _apply_softmax_power_allocation(self, power_params):\n", "        power_means = power_params[:, :, 0]  \n", "        power_vars = power_params[:, :, 1]  \n", "\n", "        # Sample from Beta distribution\n", "        power_samples = np.random.beta(power_means + 1, power_vars + 1)\n", "\n", "        # Apply SoftPlus activation\n", "        power_activated = np.log(1 + np.exp(power_samples))\n", "\n", "        # Normalize with Softmax **per user** (avoid overflow)\n", "        exp_powers = np.exp(np.clip(power_activated - np.max(power_activated, axis=1, keepdims=True), -20, 20))\n", "        return (exp_powers / np.sum(exp_powers, axis=1, keepdims=True)) * self.max_power\n", "\n", "    def _get_state(self):\n", "        epsilon = 1e-8\n", "        max_pos = np.max(np.abs(self.uav_position)) + epsilon\n", "        max_vel = np.max(np.abs(self.prev_velocity)) + epsilon\n", "        max_gain = np.max(np.abs(self.channel_gains)) + epsilon\n", "\n", "        return np.concatenate([\n", "            self.uav_position / max_pos,\n", "            self.prev_velocity / max_vel,\n", "            self.channel_gains / max_gain\n", "        ]).astype(np.float32)    \n", "    def print_power_allocations(self):\n", "        print(\"Power Allocations per User (across subchannels):\")\n", "        for user in range(self.num_users):\n", "            user_power = self.power_allocations[user]\n", "            user_power_sum = np.sum(user_power)  \n", "            print(f\"User {user}: {user_power} (Sum: {user_power_sum:.4f})\")\n", "\n", "    def print_summary(self, episode_range):\n", "        print(f\"From Episode {episode_range[0]} to {episode_range[1]} total ({episode_range[1] - episode_range[0] + 1} * 1000) timesteps,\")\n", "        print(f\"Position constraint violated: {self.position_violations} times\")\n", "        print(f\"Acceleration constraint violated: {self.acceleration_violations} times\")\n", "        \n", "        avg_users_served = np.mean(self.users_served_per_timestep)\n", "        print(f\"Average number of users served per timestep: {avg_users_served:.2f}\")\n", "\n", "        avg_throughputs = self.throughput_tracker / (len(self.users_served_per_timestep) + 1e-8)\n", "        print(\"Average throughput per user:\")\n", "        for user in range(self.num_users):\n", "            print(f\"User {user}: {avg_throughputs[user]:.4f}\")\n", "        print(\"\\n\")\n", "\n", "        self.position_violations = 0\n", "        self.acceleration_violations = 0\n", "        self.users_served_per_timestep = []  \n", "        self.throughput_tracker = np.zeros((self.num_users,)) \n", "\n", "\n", "# Create environment\n", "env = Monitor(UAVEnvironment())\n", "\n", "model = PPO(\n", "    \"MlpPolicy\",\n", "    env,\n", "    policy_kwargs=policy_kwargs,\n", "    learning_rate=0.0001,\n", "    gamma=0.99,\n", "    gae_lambda=0.98,\n", "    n_steps=10000,\n", "    batch_size=1000,\n", "    clip_range=0.2,\n", "    ent_coef=0.01,  # Entropy coefficient for exploration\n", "    verbose=1,\n", "    tensorboard_log=f\"./ppo_uav_tensorboard/PPO_{int(time.time())}\"  # Unique log for each run\n", ")\n", "# Train for 10,000,000 timesteps\n", "# Train for 10,000,000 timesteps\n", "total_timesteps = 1000000\n", "timesteps_per_episode = 1000\n", "total_episodes = total_timesteps // timesteps_per_episode  # 1000 episodes\n", "episodes_per_checkpoint = 1  # Print and plot every 20 episodes\n", "\n", "# Initialize episode counter\n", "current_episode = 0\n", "\n", "# Track cumulative rewards for early stopping\n", "cumulative_rewards = []\n", "early_stop_threshold = 0.1  # Threshold for cumulative reward change\n", "early_stop_count = 0  # Counter for episodes with small cumulative reward change\n", "\n", "# Training loop\n", "while current_episode < total_episodes:\n", "    episodes_in_chunk = min(episodes_per_checkpoint, total_episodes - current_episode)\n", "    timesteps_in_chunk = episodes_in_chunk * timesteps_per_episode\n", "    \n", "    # Reset the environment only if starting a new episode\n", "    if current_episode == 0 or current_episode % episodes_per_checkpoint == 0:\n", "        obs, _ = env.reset(options={'reset_position': True})  # Reset UAV position\n", "    else:\n", "        obs, _ = env.reset(options={'reset_position': False})  # Continue from last position\n", "    \n", "    # Train the model\n", "    model.learn(total_timesteps=timesteps_in_chunk, reset_num_timesteps=False)\n", "    current_episode += episodes_in_chunk\n", "\n", "    # Track cumulative reward\n", "    cumulative_rewards.append(env.unwrapped.cumulative_reward)\n", "\n", "    # Check for early stopping condition\n", "    if len(cumulative_rewards) >= 10:\n", "        reward_change = np.abs(cumulative_rewards[-1] - cumulative_rewards[-10])\n", "        if reward_change < early_stop_threshold:\n", "            early_stop_count += 1\n", "        else:\n", "            early_stop_count = 0\n", "\n", "        # Change user distribution if cumulative reward change is small for 10 episodes\n", "        if early_stop_count >= 10:\n", "            print(f\"Episode {current_episode}: User distribution has changed.\")\n", "            obs, _ = env.reset(options={'reset_position': True})  # Reset UAV position and user distribution\n", "            early_stop_count = 0  # Reset the counter\n", "\n", "    # Print power allocations and plot UAV movement every 20 episodes\n", "    if current_episode % episodes_per_checkpoint == 0 or current_episode >= total_episodes:\n", "        print(f\"Episode {current_episode}: Power Allocations\")\n", "        env.unwrapped.print_power_allocations()\n", "        \n", "        # Print summary of constraint violations and users served\n", "        episode_range = (current_episode - episodes_per_checkpoint + 1, current_episode)\n", "        env.unwrapped.print_summary(episode_range)\n", "        \n", "        # Simulate one episode to collect UAV positions\n", "        obs, _ = env.reset(options={'reset_position': False})  # Continue from last position\n", "        initial_position = env.unwrapped.uav_position.copy()\n", "        uav_positions = [initial_position.copy()]\n", "        for _ in range(timesteps_per_episode):  # Simulate one episode\n", "            action, _ = model.predict(obs)\n", "            obs, _, terminated, truncated, _ = env.step(action)\n", "            uav_positions.append(env.unwrapped.uav_position.copy())\n", "            if terminated or truncated:\n", "                break\n", "        final_position = env.unwrapped.uav_position.copy()\n", "        \n", "        # Plot UAV trajectory\n", "        uav_positions = np.array(uav_positions)\n", "        fig = plt.figure(figsize=(10, 8))\n", "        ax = fig.add_subplot(111, projection='3d')\n", "        ax.plot(uav_positions[:, 0], uav_positions[:, 1], uav_positions[:, 2], label=\"UAV Trajectory\", color=\"blue\")\n", "        ax.scatter(env.unwrapped.users_positions[:, 0], env.unwrapped.users_positions[:, 1], env.unwrapped.users_positions[:, 2], color=\"red\", label=\"Users\")\n", "        ax.scatter(initial_position[0], initial_position[1], initial_position[2], color=\"green\", s=100, label=\"Initial Position\")\n", "        ax.text(initial_position[0], initial_position[1], initial_position[2], \"Initial\", color=\"green\")\n", "        ax.scatter(final_position[0], final_position[1], final_position[2], color=\"purple\", s=100, label=\"Final Position\")\n", "        ax.text(final_position[0], final_position[1], final_position[2], \"Final\", color=\"purple\")\n", "        ax.set_xlabel(\"X Position\")\n", "        ax.set_ylabel(\"Y Position\")\n", "        ax.set_zlabel(\"Z Position (Altitude)\")\n", "        ax.set_title(f\"3D UAV Trajectory (Episode {current_episode})\")\n", "        ax.legend()\n", "        plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "7b1fccaa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python (sb3_env)", "language": "python", "name": "sb3_env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}