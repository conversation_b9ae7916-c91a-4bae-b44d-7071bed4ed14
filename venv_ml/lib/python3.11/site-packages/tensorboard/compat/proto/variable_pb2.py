# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/variable.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'tensorboard/compat/proto/variable.proto\x12\x0btensorboard\"\xcb\x02\n\x0bVariableDef\x12\x15\n\rvariable_name\x18\x01 \x01(\t\x12\x1a\n\x12initial_value_name\x18\x06 \x01(\t\x12\x18\n\x10initializer_name\x18\x02 \x01(\t\x12\x15\n\rsnapshot_name\x18\x03 \x01(\t\x12:\n\x13save_slice_info_def\x18\x04 \x01(\x0b\x32\x1d.tensorboard.SaveSliceInfoDef\x12\x13\n\x0bis_resource\x18\x05 \x01(\x08\x12\x11\n\ttrainable\x18\x07 \x01(\x08\x12=\n\x0fsynchronization\x18\x08 \x01(\x0e\x32$.tensorboard.VariableSynchronization\x12\x35\n\x0b\x61ggregation\x18\t \x01(\x0e\x32 .tensorboard.VariableAggregation\"`\n\x10SaveSliceInfoDef\x12\x11\n\tfull_name\x18\x01 \x01(\t\x12\x12\n\nfull_shape\x18\x02 \x03(\x03\x12\x12\n\nvar_offset\x18\x03 \x03(\x03\x12\x11\n\tvar_shape\x18\x04 \x03(\x03*\xac\x01\n\x17VariableSynchronization\x12!\n\x1dVARIABLE_SYNCHRONIZATION_AUTO\x10\x00\x12!\n\x1dVARIABLE_SYNCHRONIZATION_NONE\x10\x01\x12%\n!VARIABLE_SYNCHRONIZATION_ON_WRITE\x10\x02\x12$\n VARIABLE_SYNCHRONIZATION_ON_READ\x10\x03*\x9e\x01\n\x13VariableAggregation\x12\x1d\n\x19VARIABLE_AGGREGATION_NONE\x10\x00\x12\x1c\n\x18VARIABLE_AGGREGATION_SUM\x10\x01\x12\x1d\n\x19VARIABLE_AGGREGATION_MEAN\x10\x02\x12+\n\'VARIABLE_AGGREGATION_ONLY_FIRST_REPLICA\x10\x03\x42\x80\x01\n\x18org.tensorflow.frameworkB\x0eVariableProtosP\x01ZOgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/variable_go_proto\xf8\x01\x01\x62\x06proto3')

_VARIABLESYNCHRONIZATION = DESCRIPTOR.enum_types_by_name['VariableSynchronization']
VariableSynchronization = enum_type_wrapper.EnumTypeWrapper(_VARIABLESYNCHRONIZATION)
_VARIABLEAGGREGATION = DESCRIPTOR.enum_types_by_name['VariableAggregation']
VariableAggregation = enum_type_wrapper.EnumTypeWrapper(_VARIABLEAGGREGATION)
VARIABLE_SYNCHRONIZATION_AUTO = 0
VARIABLE_SYNCHRONIZATION_NONE = 1
VARIABLE_SYNCHRONIZATION_ON_WRITE = 2
VARIABLE_SYNCHRONIZATION_ON_READ = 3
VARIABLE_AGGREGATION_NONE = 0
VARIABLE_AGGREGATION_SUM = 1
VARIABLE_AGGREGATION_MEAN = 2
VARIABLE_AGGREGATION_ONLY_FIRST_REPLICA = 3


_VARIABLEDEF = DESCRIPTOR.message_types_by_name['VariableDef']
_SAVESLICEINFODEF = DESCRIPTOR.message_types_by_name['SaveSliceInfoDef']
VariableDef = _reflection.GeneratedProtocolMessageType('VariableDef', (_message.Message,), {
  'DESCRIPTOR' : _VARIABLEDEF,
  '__module__' : 'tensorboard.compat.proto.variable_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.VariableDef)
  })
_sym_db.RegisterMessage(VariableDef)

SaveSliceInfoDef = _reflection.GeneratedProtocolMessageType('SaveSliceInfoDef', (_message.Message,), {
  'DESCRIPTOR' : _SAVESLICEINFODEF,
  '__module__' : 'tensorboard.compat.proto.variable_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.SaveSliceInfoDef)
  })
_sym_db.RegisterMessage(SaveSliceInfoDef)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030org.tensorflow.frameworkB\016VariableProtosP\001ZOgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/variable_go_proto\370\001\001'
  _VARIABLESYNCHRONIZATION._serialized_start=489
  _VARIABLESYNCHRONIZATION._serialized_end=661
  _VARIABLEAGGREGATION._serialized_start=664
  _VARIABLEAGGREGATION._serialized_end=822
  _VARIABLEDEF._serialized_start=57
  _VARIABLEDEF._serialized_end=388
  _SAVESLICEINFODEF._serialized_start=390
  _SAVESLICEINFODEF._serialized_end=486
# @@protoc_insertion_point(module_scope)
