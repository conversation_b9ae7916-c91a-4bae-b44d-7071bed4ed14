# Virtual environments
uav_env/
venv_ml/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyTorch/TensorFlow
*.pth
*.pt
*.h5
*.pb

# Jupyter Notebook
.ipynb_checkpoints

# Environment files
.env
.venv

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Model checkpoints
checkpoints/
models/
*.zip

# Tensorboard logs
ppo_constrained_tensorboard/